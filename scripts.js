document.addEventListener("DOMContentLoaded", () => {
  // Smooth scroll for CTA link to the signup form
  const heroCta = document.querySelector('a.cta-btn[href="#signup"]');
  if (heroCta) {
    heroCta.addEventListener('click', (e) => {
      // default anchor behavior is fine for hash links; this ensures smooth experience
      e.preventDefault();
      document.getElementById('signup')?.scrollIntoView({ behavior: 'smooth' });
    });
  }

  // Basic client-side validation and feedback
  const form = document.getElementById('signup-form');
  const message = document.getElementById('form-message');
  if (form && message) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      message.className = '';
      message.textContent = '';

      const name = /** @type {HTMLInputElement} */(document.getElementById('name')).value.trim();
      const email = /** @type {HTMLInputElement} */(document.getElementById('email')).value.trim();
      const phone = /** @type {HTMLInputElement} */(document.getElementById('phone')).value.trim();

      if (!name || !email || !phone) {
        message.textContent = 'Harap lengkapi semua field.';
        message.classList.add('error');
        return;
      }

      // Simple email pattern check
      // Improved email validation regex
      const emailOk = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
      
      // Add form reset after successful submission
      if (message.classList.contains('success')) {
        setTimeout(() => {
          form.reset();
        }, 3000);
      }
      
      // Add loading state
      const submitButton = form.querySelector('button[type="submit"]');
      submitButton.innerHTML = '<span class="loading-spinner"></span> Processing...';
      submitButton.disabled = true;
      
      // Reset button after response
      submitButton.innerHTML = 'Daftar Sekarang';
      submitButton.disabled = false;
      const phoneOk = /^[0-9+\-()\s]+$/.test(phone);
      if (!emailOk) {
        message.textContent = 'Email tidak valid.';
        message.classList.add('error');
        return;
      }
      if (!phoneOk) {
        message.textContent = 'Nomor HP tidak valid.';
        message.classList.add('error');
        return;
      }

      // Simulate success (replace with real submission or redirect)
      message.textContent = 'Terima kasih! Pendaftaran berhasil.';
      message.classList.add('success');

      // Example: redirect to a registration page
      // window.location.href = 'https://example.com/register';
    });
  }
});
